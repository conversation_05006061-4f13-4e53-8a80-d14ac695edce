module github.com/metacubex/mihomo

go 1.24

require (
	github.com/bahlo/generic-list-go v0.2.0
	github.com/coreos/go-iptables v0.8.0
	github.com/dlclark/regexp2 v1.11.5
	github.com/enfein/mieru/v3 v3.19.0
	github.com/go-chi/chi/v5 v5.2.2
	github.com/go-chi/render v1.0.3
	github.com/gobwas/ws v1.4.0
	github.com/gofrs/uuid/v5 v5.3.2
	github.com/insomniacslk/dhcp v0.0.0-20250109001534-8abf58130905
	github.com/klauspost/compress v1.17.9 // lastest version compatible with golang1.20
	github.com/mdlayher/netlink v1.7.2
	github.com/metacubex/amneziawg-go v0.0.0-20250820070344-732c0c9d418a
	github.com/metacubex/bart v0.20.5
	github.com/metacubex/bbolt v0.0.0-20250725135710-010dbbbb7a5b
	github.com/metacubex/blake3 v0.1.0
	github.com/metacubex/chacha v0.1.5
	github.com/metacubex/fswatch v0.1.1
	github.com/metacubex/gopacket v1.1.20-0.20230608035415-7e2f98a3e759
	github.com/metacubex/quic-go v0.54.1-0.20250730114134-a1ae705fe295
	github.com/metacubex/randv2 v0.2.0
	github.com/metacubex/restls-client-go v0.1.7
	github.com/metacubex/sing v0.5.6-0.20250826072929-f69b475e017b
	github.com/metacubex/sing-mux v0.3.3-0.20250813083925-d7c9aeaeeaac
	github.com/metacubex/sing-quic v0.0.0-20250718154553-1b193bec4cbb
	github.com/metacubex/sing-shadowsocks v0.2.12
	github.com/metacubex/sing-shadowsocks2 v0.2.6
	github.com/metacubex/sing-shadowtls v0.0.0-20250503063515-5d9f966d17a2
	github.com/metacubex/sing-tun v0.4.8-0.20250826073243-05ab78f45ac4
	github.com/metacubex/sing-vmess v0.2.4-0.20250822020810-4856053566f0
	github.com/metacubex/sing-wireguard v0.0.0-20250503063753-2dc62acc626f
	github.com/metacubex/smux v0.0.0-20250503055512-501391591dee
	github.com/metacubex/tfo-go v0.0.0-20250516165257-e29c16ae41d4
	github.com/metacubex/utls v1.8.1-0.20250823120917-12f5ba126142
	github.com/metacubex/wireguard-go v0.0.0-20250820062549-a6cecdd7f57f
	github.com/miekg/dns v1.1.63 // lastest version compatible with golang1.20
	github.com/mroth/weightedrand/v2 v2.1.0
	github.com/openacid/low v0.1.21
	github.com/oschwald/maxminddb-golang v1.12.0 // lastest version compatible with golang1.20
	github.com/sagernet/cors v1.2.1
	github.com/sagernet/netlink v0.0.0-20240612041022-b9a21c07ac6a
	github.com/samber/lo v1.51.0
	github.com/shirou/gopsutil/v4 v4.25.1 // lastest version compatible with golang1.20
	github.com/sirupsen/logrus v1.9.3
	github.com/stretchr/testify v1.10.0
	github.com/vmihailenco/msgpack/v5 v5.4.1
	github.com/wk8/go-ordered-map/v2 v2.1.8
	gitlab.com/go-extension/aes-ccm v0.0.0-20230221065045-e58665ef23c7
	go.uber.org/automaxprocs v1.6.0
	go4.org/netipx v0.0.0-20231129151722-fdeea329fbba
	golang.org/x/crypto v0.33.0 // lastest version compatible with golang1.20
	golang.org/x/exp v0.0.0-20240904232852-e7e105dedf7e // lastest version compatible with golang1.20
	golang.org/x/net v0.35.0 // lastest version compatible with golang1.20
	golang.org/x/sync v0.11.0 // lastest version compatible with golang1.20
	golang.org/x/sys v0.30.0 // lastest version compatible with golang1.20
	google.golang.org/protobuf v1.34.2 // lastest version compatible with golang1.20
	gopkg.in/yaml.v3 v3.0.1
)

require github.com/dmitryikh/leaves v0.0.0-20230708180554-25d19a787328

require (
	github.com/RyuaNerin/go-krypto v1.3.0 // indirect
	github.com/Yawning/aez v0.0.0-20211027044916-e49e68abd344 // indirect
	github.com/ajg/form v1.5.1 // indirect
	github.com/andybalholm/brotli v1.0.6 // indirect
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/ebitengine/purego v0.8.3 // indirect
	github.com/ericlagergren/aegis v0.0.0-20250325060835-cd0defd64358 // indirect
	github.com/ericlagergren/polyval v0.0.0-20220411101811-e25bc10ba391 // indirect
	github.com/ericlagergren/siv v0.0.0-20220507050439-0b757b3aa5f1 // indirect
	github.com/ericlagergren/subtle v0.0.0-20220507045147-890d697da010 // indirect
	github.com/fsnotify/fsnotify v1.9.0 // indirect
	github.com/gaukas/godicttls v0.0.4 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/google/btree v1.1.3 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/pprof v0.0.0-20210407192527-94a9f03dee38 // indirect
	github.com/hashicorp/yamux v0.1.2 // indirect
	github.com/josharian/native v1.1.0 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mdlayher/socket v0.4.1 // indirect
	github.com/metacubex/ascon v0.1.0 // indirect
	github.com/metacubex/gvisor v0.0.0-20250826025146-23043f716a2c // indirect
	github.com/metacubex/nftables v0.0.0-20250503052935-30a69ab87793 // indirect
	github.com/oasisprotocol/deoxysii v0.0.0-20220228165953-2091330c22b7 // indirect
	github.com/onsi/ginkgo/v2 v2.9.5 // indirect
	github.com/pierrec/lz4/v4 v4.1.14 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/quic-go/qpack v0.4.0 // indirect
	github.com/sina-ghaderi/poly1305 v0.0.0-20220724002748-c5926b03988b // indirect
	github.com/sina-ghaderi/rabaead v0.0.0-20220730151906-ab6e06b96e8c // indirect
	github.com/sina-ghaderi/rabbitio v0.0.0-20220730151941-9ce26f4f872e // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/u-root/uio v0.0.0-20230220225925-ffce2a382923 // indirect
	github.com/vishvananda/netns v0.0.4 // indirect
	github.com/vmihailenco/tagparser/v2 v2.0.0 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	gitlab.com/yawning/bsaes.git v0.0.0-20190805113838-0a714cd429ec // indirect
	go.uber.org/mock v0.4.0 // indirect
	golang.org/x/mod v0.20.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	golang.org/x/time v0.7.0 // indirect
	golang.org/x/tools v0.24.0 // indirect
)
