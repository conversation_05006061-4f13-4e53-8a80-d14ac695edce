package race

import (
	"context"
	"errors"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"github.com/metacubex/mihomo/common/buf"
	C "github.com/metacubex/mihomo/constant"
)

type command func(won bool, conn C.Conn) (any, error)
type Callback func(proxy C.Proxy, conn C.Conn, err error) // on the first read via each proxy

var ErrNoAlive = errors.New("no alive")

type racer struct {
	conn  C.Conn
	proxy C.Proxy
	ready chan struct{}
	alive atomic.Bool
	done  chan struct{}
}

func (racer *racer) close() error {
	if !racer.alive.Load() {
		return nil
	}
	racer.alive.Store(false)
	return racer.conn.Close()
}

func DialContext(ctx context.Context, metadata *C.Metadata, cb Callback, proxies ...C.Proxy) (C.Conn, error) {
	if len(proxies) == 0 {
		return nil, errors.New("no proxy")
	}
	if len(proxies) == 1 {
		return proxies[0].DialContext(ctx, metadata)
	}

	race := newRaceConn(cb, proxies...)
	once := sync.Once{}
	hit := make(chan struct{})
	wg := sync.WaitGroup{}
	miss := make(chan struct{})
	errs := make([]error, len(race.racers))

	wg.Add(len(race.racers))
	go func() {
		wg.Wait()
		close(miss)
	}()

	for i := range race.racers {
		n := i
		r := &race.racers[n]
		go func() {
			defer wg.Done()
			defer close(r.ready)
			conn, err := r.proxy.DialContext(ctx, metadata)
			if err == nil {
				r.conn = conn
				r.alive.Store(true)
				once.Do(func() {
					race.leader.Store(r) // always not nil
					close(hit)
				})
			} else {
				errs[n] = err
			}
		}()
	}

	race.goWrite()

	select {
	case <-hit:
		return race, nil
	case <-miss:
		return nil, joinOrNoAlive(errs...)
	}
}

type raceConn struct {
	racers []racer
	once   sync.Once // read
	cb     Callback
	mu     sync.Mutex
	cond   sync.Cond
	writes []command
	wg     sync.WaitGroup // write
	leader atomic.Pointer[racer]
	winner *racer
	steady atomic.Bool
	chain  []C.ProxyAdapter
}

func (rc *raceConn) Read(b []byte) (int, error) {
	if rc.steady.Load() {
		return rc.winner.conn.Read(b)
	}
	type result struct {
		w bool
		b []byte
		n int
	}
	res, err := rc.doRead(func(won bool, conn C.Conn) (any, error) {
		var bs []byte
		if won {
			bs = b
		} else {
			bs = make([]byte, len(b), cap(b))
		}
		n, err := conn.Read(bs)
		return &result{won, bs, n}, err
	})
	r := res.(*result)
	if !r.w {
		copy(b, r.b)
	}
	return r.n, err
}

func (rc *raceConn) ReadBuffer(b *buf.Buffer) error {
	if rc.steady.Load() {
		return rc.winner.conn.ReadBuffer(b)
	}
	type result struct {
		w bool
		b *buf.Buffer
	}
	res, err := rc.doRead(func(won bool, conn C.Conn) (any, error) {
		var bu *buf.Buffer
		if won {
			bu = b
		} else {
			bu = buf.NewSize(b.RawCap())
		}
		return &result{won, bu}, conn.ReadBuffer(bu)
	})
	r := res.(*result)
	if !r.w && err == nil {
		_, err = b.ReadFrom(r.b)
	}
	return err
}

func (rc *raceConn) Write(b []byte) (int, error) {
	if rc.steady.Load() {
		return rc.winner.conn.Write(b)
	}
	n, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return conn.Write(b)
	})
	return n.(int), err
}

func (rc *raceConn) WriteBuffer(b *buf.Buffer) error {
	if rc.steady.Load() {
		return rc.winner.conn.WriteBuffer(b)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		var bu *buf.Buffer
		if won {
			bu = b
		} else {
			bu = b.ToOwned()
		}
		return nil, conn.WriteBuffer(bu)
	})
	return err
}

func (rc *raceConn) Close() error {
	if rc.steady.Load() {
		return rc.winner.close()
	}
	defer rc.wg.Wait()
	rc.mu.Lock()
	if rc.winner != nil {
		rc.mu.Unlock()
		err := rc.winner.close()
		return err
	}
	rc.mu.Unlock()
	errs := make([]error, len(rc.racers))
	wg := sync.WaitGroup{}
	wg.Add(len(rc.racers))
	for i := range rc.racers {
		n := i
		r := &rc.racers[n]
		go func() {
			defer wg.Done()
			<-r.ready
			errs[n] = r.close()
		}()
	}
	wg.Wait()
	rc.mu.Lock()
	rc.cond.Broadcast()
	rc.mu.Unlock()
	return errors.Join(errs...)
}

func (rc *raceConn) LocalAddr() net.Addr {
	return rc.leader.Load().conn.LocalAddr()
}

func (rc *raceConn) RemoteAddr() net.Addr {
	return rc.leader.Load().conn.RemoteAddr()
}

func (rc *raceConn) SetDeadline(t time.Time) error {
	if rc.steady.Load() {
		return rc.winner.conn.SetDeadline(t)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return nil, conn.SetDeadline(t)
	})
	return err
}

func (rc *raceConn) SetReadDeadline(t time.Time) error {
	if rc.steady.Load() {
		return rc.winner.conn.SetReadDeadline(t)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return nil, conn.SetReadDeadline(t)
	})
	return err
}

func (rc *raceConn) SetWriteDeadline(t time.Time) error {
	if rc.steady.Load() {
		return rc.winner.conn.SetWriteDeadline(t)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return nil, conn.SetWriteDeadline(t)
	})
	return err
}

func (rc *raceConn) Chains() C.Chain {
	if rc.steady.Load() {
		return rc.winner.conn.Chains()
	}
	rc.mu.Lock()
	defer rc.mu.Unlock()
	conn := rc.leader.Load().conn
	if rc.chain == nil {
		return conn.Chains()
	}
	ret := make(C.Chain, 0, len(rc.chain)+len(conn.Chains()))
	ret = append(ret, conn.Chains()...)
	for _, a := range rc.chain {
		ret = append(ret, a.Name())
	}
	return ret
}

func (rc *raceConn) AppendToChains(a C.ProxyAdapter) {
	if rc.steady.Load() {
		rc.winner.conn.AppendToChains(a)
		return
	}
	rc.mu.Lock()
	defer rc.mu.Unlock()
	if rc.chain != nil {
		rc.chain = append(rc.chain, a)
	} else {
		rc.winner.conn.AppendToChains(a)
	}
}

func (rc *raceConn) RemoteDestination() string {
	return rc.leader.Load().conn.RemoteDestination()
}

func (rc *raceConn) Upstream() any {
	if rc.steady.Load() {
		return rc.winner.conn
	}
	return nil
}

func (rc *raceConn) WriterReplaceable() bool {
	return rc.steady.Load()
}

func (rc *raceConn) ReaderReplaceable() bool {
	return rc.steady.Load()
}

func newRaceConn(cb Callback, proxies ...C.Proxy) *raceConn {
	ret := &raceConn{}
	ret.racers = make([]racer, len(proxies))
	ret.cb = cb
	ret.writes = make([]command, 0, 1)
	ret.chain = make([]C.ProxyAdapter, 0, 1)
	ret.cond.L = &ret.mu
	for i := range ret.racers {
		r := &ret.racers[i]
		r.proxy = proxies[i]
		r.ready = make(chan struct{})
		r.done = make(chan struct{})
	}
	return ret
}

func (rc *raceConn) goWrite() {
	rc.wg.Add(len(rc.racers))
	for i := range rc.racers {
		r := &rc.racers[i]
		go func() {
			defer rc.wg.Done()
			defer close(r.done)

			pos := 0
			wait := func() bool {
				rc.mu.Lock()
				defer rc.mu.Unlock()
				for {
					if !r.alive.Load() {
						return false
					}
					if rc.winner == nil {
						if pos < len(rc.writes) {
							return true
						}
						rc.cond.Wait()
					} else {
						if rc.winner == r {
							return pos < len(rc.writes)
						}
						_ = r.close()
						return false
					}
				}
			}

			<-r.ready
			for {
				if !wait() {
					return
				}
				_, err := rc.writes[pos](false, r.conn)
				pos++
				if err != nil {
					_ = r.close()
					return
				}
			}
		}()
	}
}

func (rc *raceConn) doWrite(cmd command) (any, error) {
	rc.mu.Lock()
	if rc.winner != nil {
		rc.mu.Unlock()
		<-rc.winner.done
		rc.steady.Store(true)
		return cmd(true, rc.winner.conn)
	}

	var wres any
	once := sync.Once{}
	hit := make(chan struct{})
	miss := make(chan struct{})
	errs := make([]error, len(rc.racers))

	go func() {
		rc.wg.Wait() // since write loop breaks on any error
		close(miss)
	}()

	var n int32 = 0
	rc.writes = append(rc.writes, func(win bool, conn C.Conn) (any, error) {
		res, err := cmd(win, conn)
		if err == nil {
			once.Do(func() {
				wres = res
				close(hit)
			})
		} else {
			errs[atomic.AddInt32(&n, 1)-1] = err
		}
		return res, err
	})
	rc.cond.Broadcast()
	rc.mu.Unlock()

	select {
	case <-hit:
		return wres, nil
	case <-miss:
		return nil, joinOrNoAlive(errs...)
	}
}

func (rc *raceConn) doRead(cmd command) (any, error) {
	var rres any
	var rerr error
	did := false
	rc.once.Do(func() {
		did = true

		once := sync.Once{}
		hit := make(chan struct{})
		wg := sync.WaitGroup{}
		miss := make(chan struct{})
		errs := make([]error, len(rc.racers))

		wg.Add(len(rc.racers))
		go func() {
			wg.Wait()
			close(miss)
			rc.wg.Wait()
			rc.mu.Lock()
			if rc.winner != nil {
				// no writes will access
				rc.racers = nil
				rc.writes = nil
			}
			rc.mu.Unlock()
		}()

		for i := range rc.racers {
			n := i
			r := &rc.racers[n]

			go func() {
				defer wg.Done()
				<-r.ready
				if !r.alive.Load() {
					return
				}
				lead := false
				defer func() {
					if !lead {
						_ = r.close()
					}
				}()
				rc.mu.Lock()
				winner := rc.winner
				rc.mu.Unlock()
				if winner != nil {
					return
				}
				res, err := cmd(false, r.conn)
				rc.cb(r.proxy, r.conn, err)
				if err == nil {
					once.Do(func() {
						rres = res
						rc.mu.Lock()
						rc.winner = r
						rc.leader.Store(r)
						for _, a := range rc.chain {
							r.conn.AppendToChains(a)
						}
						rc.chain = nil
						rc.cond.Broadcast()
						rc.mu.Unlock()
						lead = true
						close(hit)
					})
				} else {
					errs[n] = err
				}
			}()
		}

		select {
		case <-hit:
			rerr = nil
			return
		case <-miss:
			rerr = joinOrNoAlive(errs...)
			return
		}
	})

	if did {
		return rres, rerr
	}

	if rc.winner == nil { // without lock since it won't change anymore
		return nil, ErrNoAlive
	}

	return cmd(true, rc.winner.conn)
}

func joinOrNoAlive(errs ...error) error {
	err := errors.Join(errs...)
	if err == nil {
		return ErrNoAlive
	}
	return err
}
