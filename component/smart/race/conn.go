package race

import (
	"context"
	"errors"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"github.com/metacubex/mihomo/common/buf"
	C "github.com/metacubex/mihomo/constant"
)

type Command func(won bool, conn C.Conn) (any, error)

var ErrNoAlive = errors.New("no alive")

type racer struct {
	conn  C.Conn
	mu    sync.Mutex
	alive bool
	done  chan struct{}
}

func (racer *racer) isAlive() bool {
	racer.mu.Lock()
	defer racer.mu.Unlock()
	return racer.alive
}

func (racer *racer) close() error {
	racer.mu.Lock()
	defer racer.mu.Unlock()
	if !racer.alive {
		return nil
	}
	racer.alive = false
	return racer.conn.Close()
}

func DialContext(ctx context.Context, metadata *C.Metadata, proxies ...C.Proxy) (C.Conn, error) {
	if len(proxies) == 0 {
		return nil, errors.New("no proxy")
	}
	if len(proxies) == 1 {
		return proxies[0].DialContext(ctx, metadata)
	}

	race := newRaceConn(len(proxies))
	once := sync.Once{}
	hit := make(chan struct{})
	wg := sync.WaitGroup{}
	miss := make(chan struct{})
	errs := make([]error, len(race.racers))

	wg.Add(len(race.racers))
	go func() {
		wg.Wait()
		close(miss)
	}()

	for i := range race.racers {
		n := i
		r := &race.racers[n]
		r.mu.Lock()
		go func() {
			defer wg.Done()
			defer r.mu.Unlock()
			conn, err := proxies[n].DialContext(ctx, metadata)
			if err == nil {
				r.conn = conn
				r.alive = true
				once.Do(func() {
					race.lead = r
					close(hit)
				})
			} else {
				errs[n] = err
			}
		}()
	}

	race.goWrite()

	select {
	case <-hit:
		return race, nil
	case <-miss:
		return nil, joinOrNoAlive(errs...)
	}
}

type raceConn struct {
	racers []racer
	once   sync.Once // read
	mu     sync.Mutex
	cond   sync.Cond
	writes []Command
	wg     sync.WaitGroup // write
	lead   *racer         // first connected
	winner *racer
	steady atomic.Bool
	chain  []C.ProxyAdapter
}

func (rc *raceConn) Read(b []byte) (int, error) {
	if rc.steady.Load() {
		return rc.winner.conn.Read(b)
	}
	type result struct {
		w bool
		b []byte
		n int
	}

	res, err := rc.doRead(func(won bool, conn C.Conn) (any, error) {
		var bs []byte
		if won {
			bs = b
		} else {
			bs = make([]byte, len(b), cap(b))
		}
		n, err := conn.Read(bs)
		return &result{false, bs, n}, err
	})
	r := res.(*result)
	if !r.w {
		copy(b, r.b)
	}
	return r.n, err
}

func (rc *raceConn) ReadBuffer(b *buf.Buffer) error {
	if rc.steady.Load() {
		return rc.winner.conn.ReadBuffer(b)
	}
	type result struct {
		w bool
		b *buf.Buffer
	}

	res, err := rc.doRead(func(won bool, conn C.Conn) (any, error) {
		var bu *buf.Buffer
		if won {
			bu = b
		} else {
			bu = buf.NewSize(b.RawCap())
		}
		return &result{false, bu}, conn.ReadBuffer(bu)
	})
	r := res.(*result)
	if !r.w && err == nil {
		_, err = b.ReadFrom(r.b)
	}
	return err
}

func (rc *raceConn) Write(b []byte) (int, error) {
	if rc.steady.Load() {
		return rc.winner.conn.Write(b)
	}
	n, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return conn.Write(b)
	})
	return n.(int), err
}

func (rc *raceConn) WriteBuffer(b *buf.Buffer) error {
	if rc.steady.Load() {
		return rc.winner.conn.WriteBuffer(b)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		var bu *buf.Buffer
		if won {
			bu = b
		} else {
			bu = b.ToOwned()
		}
		return nil, conn.WriteBuffer(bu)
	})
	return err
}

func (rc *raceConn) Close() error {
	if rc.steady.Load() {
		return rc.winner.close()
	}
	defer rc.wg.Wait()
	rc.mu.Lock()
	if rc.winner != nil {
		err := rc.winner.close()
		rc.mu.Unlock()
		return err
	}
	errs := make([]error, len(rc.racers))
	wg := sync.WaitGroup{}
	wg.Add(len(rc.racers))
	for i := range rc.racers {
		n := i
		r := &rc.racers[n]
		go func() {
			defer wg.Done()
			errs[n] = r.close()
		}()
	}
	rc.mu.Unlock()
	wg.Wait()
	return errors.Join(errs...)
}

func (rc *raceConn) getConn() C.Conn {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	if rc.winner != nil {
		return rc.winner.conn
	}
	return rc.lead.conn
}

func (rc *raceConn) LocalAddr() net.Addr {
	if rc.steady.Load() {
		return rc.winner.conn.LocalAddr()
	}
	return rc.getConn().LocalAddr()
}

func (rc *raceConn) RemoteAddr() net.Addr {
	if rc.steady.Load() {
		return rc.winner.conn.RemoteAddr()
	}
	return rc.getConn().RemoteAddr()
}

func (rc *raceConn) SetDeadline(t time.Time) error {
	if rc.steady.Load() {
		return rc.winner.conn.SetDeadline(t)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return nil, conn.SetDeadline(t)
	})
	return err
}

func (rc *raceConn) SetReadDeadline(t time.Time) error {
	if rc.steady.Load() {
		return rc.winner.conn.SetReadDeadline(t)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return nil, conn.SetReadDeadline(t)
	})
	return err
}

func (rc *raceConn) SetWriteDeadline(t time.Time) error {
	if rc.steady.Load() {
		return rc.winner.conn.SetWriteDeadline(t)
	}
	_, err := rc.doWrite(func(won bool, conn C.Conn) (any, error) {
		return nil, conn.SetWriteDeadline(t)
	})
	return err
}

func (rc *raceConn) Chains() C.Chain {
	if rc.steady.Load() {
		return rc.winner.conn.Chains()
	}
	rc.mu.Lock()
	defer rc.mu.Unlock()
	var conn C.Conn
	if rc.winner != nil {
		conn = rc.winner.conn
	} else {
		conn = rc.lead.conn
	}
	if rc.chain == nil {
		return conn.Chains()
	}
	ret := make(C.Chain, 0, len(rc.chain)+len(conn.Chains()))
	ret = append(ret, conn.Chains()...)
	for _, a := range rc.chain {
		ret = append(ret, a.Name())
	}
	return ret
}

func (rc *raceConn) AppendToChains(a C.ProxyAdapter) {
	if rc.steady.Load() {
		rc.winner.conn.AppendToChains(a)
		return
	}
	rc.mu.Lock()
	defer rc.mu.Unlock()
	if rc.chain != nil {
		rc.chain = append(rc.chain, a)
	} else {
		rc.winner.conn.AppendToChains(a)
	}
}

func (rc *raceConn) RemoteDestination() string {
	if rc.steady.Load() {
		return rc.winner.conn.RemoteDestination()
	}
	return rc.getConn().RemoteDestination()
}

func (rc *raceConn) Upstream() any {
	if rc.steady.Load() {
		return rc.winner.conn
	}
	return nil
}

func (rc *raceConn) WriterReplaceable() bool {
	return rc.steady.Load()
}

func (rc *raceConn) ReaderReplaceable() bool {
	return rc.steady.Load()
}

func newRaceConn(size int) *raceConn {
	ret := &raceConn{}
	ret.racers = make([]racer, size)
	ret.writes = make([]Command, 0, 1)
	ret.chain = make([]C.ProxyAdapter, 0, 1)
	ret.cond.L = &ret.mu
	for i := range ret.racers {
		r := &ret.racers[i]
		r.done = make(chan struct{})
	}
	return ret
}

func (rc *raceConn) goWrite() {
	rc.wg.Add(len(rc.racers))
	for i := range rc.racers {
		r := &rc.racers[i]
		go func() {
			defer rc.wg.Done()
			defer close(r.done)

			pos := 0
			wait := func() bool {
				rc.mu.Lock()
				defer rc.mu.Unlock()
				for {
					if !r.isAlive() {
						return false
					}
					if rc.winner == nil {
						if pos < len(rc.writes) {
							return true
						}
						rc.cond.Wait()
					} else {
						if rc.winner == r {
							return pos < len(rc.writes)
						}
						_ = r.close()
						return false
					}
				}
			}

			for {
				if !wait() {
					return
				}
				_, err := rc.writes[pos](false, r.conn)
				pos++
				if err != nil {
					_ = r.close()
					return
				}
			}
		}()
	}
}

func (rc *raceConn) doWrite(cmd Command) (any, error) {
	rc.mu.Lock()
	if rc.winner != nil {
		rc.mu.Unlock()
		<-rc.winner.done
		rc.steady.Store(true)
		return cmd(true, rc.winner.conn)
	}

	var wres any
	once := sync.Once{}
	hit := make(chan struct{})
	miss := make(chan struct{})
	errs := make([]error, len(rc.racers))

	go func() {
		rc.wg.Wait()
		close(miss)
	}()

	var n int32 = 0
	rc.writes = append(rc.writes, func(win bool, conn C.Conn) (any, error) {
		res, err := cmd(win, conn)
		if err == nil {
			once.Do(func() {
				wres = res
				close(hit)
			})
		} else {
			errs[atomic.AddInt32(&n, 1)-1] = err
		}
		return res, err
	})
	rc.mu.Unlock()
	rc.cond.Broadcast()

	select {
	case <-hit:
		return wres, nil
	case <-miss:
		return nil, joinOrNoAlive(errs...)
	}
}

func (rc *raceConn) doRead(cmd Command) (any, error) {
	var rres any
	var rerr error
	did := false
	rc.once.Do(func() {
		did = true

		once := sync.Once{}
		hit := make(chan struct{})
		wg := sync.WaitGroup{}
		miss := make(chan struct{})
		errs := make([]error, len(rc.racers))

		wg.Add(len(rc.racers))
		go func() {
			wg.Wait()
			close(miss)
		}()

		for i := range rc.racers {
			n := i
			r := &rc.racers[n]

			go func() {
				defer wg.Done()
				if !r.isAlive() {
					return
				}
				first := false
				defer func() {
					if !first {
						_ = r.close()
					}
				}()
				rc.mu.Lock()
				winner := rc.winner
				rc.mu.Unlock()
				if winner != nil {
					return
				}
				res, err := cmd(false, r.conn)
				if err == nil {
					once.Do(func() {
						rres = res
						rc.mu.Lock()
						rc.winner = r
						for _, a := range rc.chain {
							r.conn.AppendToChains(a)
						}
						rc.chain = nil
						rc.mu.Unlock()
						rc.cond.Broadcast()
						first = true
						close(hit)
					})
				} else {
					errs[n] = err
				}
			}()
		}

		select {
		case <-hit:
			rerr = nil
			return
		case <-miss:
			rerr = joinOrNoAlive(errs...)
			return
		}
	})

	if did {
		go func() {
			rc.wg.Wait()
			rc.mu.Lock()
			if rc.winner != nil {
				rc.racers = nil
				rc.writes = nil
				rc.lead = nil
			}
			rc.mu.Unlock()
		}()
		return rres, rerr
	}

	if rc.winner == nil {
		return nil, ErrNoAlive
	}

	return cmd(true, rc.winner.conn)
}

func joinOrNoAlive(errs ...error) error {
	err := errors.Join(errs...)
	if err == nil {
		return ErrNoAlive
	}
	return err
}
